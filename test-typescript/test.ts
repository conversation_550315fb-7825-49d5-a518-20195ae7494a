import { readFileSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

const data = readFileSync('tests/data/shred_000003.bin')
const { entries } = decode_entries(1n, data)

for (const entry of entries) {
    console.log('Entry', entry.hash, entry.num_hashes, entry.transactions.length)

    for (const tx of entry.transactions) {
        console.log(`  Transaction: ${tx.signatures.length} signatures`)

        for (const sig of tx.signatures) {
            console.log(`    Signature: ${sig}`)
        }
    }
}
