import { readFileSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

function inspectObject(obj: any, depth = 0, maxDepth = 5): string {
    const indent = '  '.repeat(depth)

    if (depth > maxDepth) {
        return `${indent}[Max depth reached]`
    }

    if (obj === null) return `${indent}null`
    if (obj === undefined) return `${indent}undefined`

    const type = typeof obj
    if (type === 'string' || type === 'number' || type === 'boolean') {
        return `${indent}${type}: ${obj}`
    }

    if (Array.isArray(obj)) {
        if (obj.length === 0) return `${indent}Array(0): []`
        if (obj.length <= 5) {
            return `${indent}Array(${obj.length}): [${obj
                .map((item) => (typeof item === 'object' ? `{...}` : item))
                .join(', ')}]`
        }
        return `${indent}Array(${obj.length}): [${obj
            .slice(0, 3)
            .map((item) => (typeof item === 'object' ? `{...}` : item))
            .join(', ')}, ...]`
    }

    if (type === 'object') {
        const constructor = obj.constructor?.name || 'Object'
        const keys = Object.keys(obj)

        if (keys.length === 0) {
            return `${indent}${constructor}: {}`
        }

        let result = `${indent}${constructor}: {\n`
        for (const key of keys) {
            result += `${indent}  ${key}: `
            const value = obj[key]
            if (typeof value === 'object' && value !== null) {
                result += `\n${inspectObject(value, depth + 2, maxDepth)}\n`
            } else {
                result += `${typeof value}: ${value}\n`
            }
        }
        result += `${indent}}`
        return result
    }

    return `${indent}${type}: ${obj}`
}

console.log('=== TESTING TYPESCRIPT TYPE DEFINITIONS VS ACTUAL DATA ===\n')

const data = readFileSync('tests/data/shred_000003.bin')
console.log(`Input data size: ${data.length} bytes\n`)

const result = decode_entries(1n, data)
console.log('=== DECODE RESULT STRUCTURE ===')
console.log(inspectObject(result))

console.log('\n=== DETAILED ANALYSIS ===')
console.log(`Result type: ${typeof result}`)
console.log(`Result constructor: ${result?.constructor?.name}`)
console.log(`Result keys: ${Object.keys(result || {})}`)

if (result && 'entries' in result) {
    const { entries } = result
    console.log(`\nEntries array length: ${entries?.length}`)
    console.log(`Entries array type: ${typeof entries}`)
    console.log(`Entries is array: ${Array.isArray(entries)}`)

    if (Array.isArray(entries) && entries.length > 0) {
        console.log('\n=== FIRST ENTRY DETAILED INSPECTION ===')
        const firstEntry = entries[0]
        console.log(inspectObject(firstEntry))

        console.log('\n=== ENTRY FIELDS ANALYSIS ===')
        console.log(`Entry type: ${typeof firstEntry}`)
        console.log(`Entry constructor: ${firstEntry?.constructor?.name}`)
        console.log(`Entry keys: ${Object.keys(firstEntry || {})}`)

        // Analyze hash field
        if ('hash' in firstEntry) {
            console.log(`\nHash field type: ${typeof firstEntry.hash}`)
            console.log(`Hash constructor: ${firstEntry.hash?.constructor?.name}`)
            if (firstEntry.hash && typeof firstEntry.hash === 'object') {
                console.log(`Hash keys: ${Object.keys(firstEntry.hash)}`)
                console.log(`Hash toString(): ${firstEntry.hash.toString?.()}`)
                console.log(`Hash toBytes(): ${firstEntry.hash.toBytes?.()}`)
            }
        }

        // Analyze num_hashes field
        if ('num_hashes' in firstEntry) {
            console.log(`\nnum_hashes type: ${typeof firstEntry.num_hashes}`)
            console.log(`num_hashes value: ${firstEntry.num_hashes}`)
        }

        // Analyze transactions field
        if ('transactions' in firstEntry) {
            console.log(`\nTransactions type: ${typeof firstEntry.transactions}`)
            console.log(`Transactions is array: ${Array.isArray(firstEntry.transactions)}`)
            console.log(`Transactions length: ${firstEntry.transactions?.length}`)

            if (Array.isArray(firstEntry.transactions) && firstEntry.transactions.length > 0) {
                console.log('\n=== FIRST TRANSACTION DETAILED INSPECTION ===')
                const firstTx = firstEntry.transactions[0]
                console.log(inspectObject(firstTx))

                console.log('\n=== TRANSACTION FIELDS ANALYSIS ===')
                console.log(`Transaction type: ${typeof firstTx}`)
                console.log(`Transaction constructor: ${firstTx?.constructor?.name}`)
                console.log(`Transaction keys: ${Object.keys(firstTx || {})}`)

                // Analyze signatures
                if ('signatures' in firstTx) {
                    console.log(`\nSignatures type: ${typeof firstTx.signatures}`)
                    console.log(`Signatures is array: ${Array.isArray(firstTx.signatures)}`)
                    console.log(`Signatures length: ${firstTx.signatures?.length}`)

                    if (Array.isArray(firstTx.signatures) && firstTx.signatures.length > 0) {
                        const firstSig = firstTx.signatures[0]
                        console.log(`First signature type: ${typeof firstSig}`)
                        console.log(`First signature is array: ${Array.isArray(firstSig)}`)
                        if (Array.isArray(firstSig)) {
                            console.log(`First signature length: ${firstSig.length}`)
                            console.log(`First signature sample: [${firstSig.slice(0, 5).join(', ')}...]`)
                        }
                    }
                }

                // Analyze message
                if ('message' in firstTx) {
                    console.log(`\nMessage type: ${typeof firstTx.message}`)
                    console.log(`Message constructor: ${firstTx.message?.constructor?.name}`)
                    console.log(`Message keys: ${Object.keys(firstTx.message || {})}`)
                    console.log(inspectObject(firstTx.message, 0, 3))
                }
            }
        }
    }
} else {
    console.log('\nNo entries found in result or entries field missing')
}

console.log('\n=== TYPE DEFINITIONS COMPARISON ===')
console.log('Expected according to .d.ts file:')
console.log('- decode_entries should return: ParsedEntry')
console.log('- ParsedEntry should have: { slot: number, entries: Entry[] }')
console.log('- Entry should have: { num_hashes: number, hash: Hash, transactions: VersionedTransaction[] }')
console.log('- Hash should be a class with toString(), toBytes(), equals() methods')
console.log('- Signature should be: number[]')
console.log('- VersionedTransaction should have: { signatures: Signature[], message: VersionedMessage }')
