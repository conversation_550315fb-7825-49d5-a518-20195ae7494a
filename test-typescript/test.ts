import { readFileSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

function inspectObject(obj: any, depth = 0, maxDepth = 5): string {
    const indent = '  '.repeat(depth)

    if (depth > maxDepth) {
        return `${indent}[Max depth reached]`
    }

    if (obj === null) return `${indent}null`
    if (obj === undefined) return `${indent}undefined`

    const type = typeof obj
    if (type === 'string' || type === 'number' || type === 'boolean') {
        return `${indent}${type}: ${obj}`
    }

    if (Array.isArray(obj)) {
        if (obj.length === 0) return `${indent}Array(0): []`
        if (obj.length <= 5) {
            return `${indent}Array(${obj.length}): [${obj
                .map((item) => (typeof item === 'object' ? `{...}` : item))
                .join(', ')}]`
        }
        return `${indent}Array(${obj.length}): [${obj
            .slice(0, 3)
            .map((item) => (typeof item === 'object' ? `{...}` : item))
            .join(', ')}, ...]`
    }

    if (type === 'object') {
        const constructor = obj.constructor?.name || 'Object'
        const keys = Object.keys(obj)

        if (keys.length === 0) {
            return `${indent}${constructor}: {}`
        }

        let result = `${indent}${constructor}: {\n`
        for (const key of keys) {
            result += `${indent}  ${key}: `
            const value = obj[key]
            if (typeof value === 'object' && value !== null) {
                result += `\n${inspectObject(value, depth + 2, maxDepth)}\n`
            } else {
                result += `${typeof value}: ${value}\n`
            }
        }
        result += `${indent}}`
        return result
    }

    return `${indent}${type}: ${obj}`
}

console.log('=== TESTING TYPESCRIPT TYPE DEFINITIONS VS ACTUAL DATA ===\n')

const data = readFileSync('tests/data/shred_000003.bin')
console.log(`Input data size: ${data.length} bytes\n`)

const result = decode_entries(1n, data)
console.log('=== DECODE RESULT STRUCTURE ===')
console.log(inspectObject(result))

console.log('\n=== DETAILED ANALYSIS ===')
console.log(`Result type: ${typeof result}`)
console.log(`Result constructor: ${result?.constructor?.name}`)
console.log(`Result keys: ${Object.keys(result || {})}`)

if (result && 'entries' in result) {
    const { entries } = result
    console.log(`\nEntries array length: ${entries?.length}`)
    console.log(`Entries array type: ${typeof entries}`)
    console.log(`Entries is array: ${Array.isArray(entries)}`)

    if (Array.isArray(entries) && entries.length > 0) {
        console.log('\n=== FIRST ENTRY DETAILED INSPECTION ===')
        const firstEntry = entries[0]
        console.log(inspectObject(firstEntry))

        console.log('\n=== ENTRY FIELDS ANALYSIS ===')
        console.log(`Entry type: ${typeof firstEntry}`)
        console.log(`Entry constructor: ${firstEntry?.constructor?.name}`)
        console.log(`Entry keys: ${Object.keys(firstEntry || {})}`)

        // Analyze hash field
        if ('hash' in firstEntry) {
            console.log(`\nHash field type: ${typeof firstEntry.hash}`)
            console.log(`Hash constructor: ${firstEntry.hash?.constructor?.name}`)
            if (firstEntry.hash && typeof firstEntry.hash === 'object') {
                console.log(`Hash keys: ${Object.keys(firstEntry.hash)}`)
                console.log(`Hash toString(): ${firstEntry.hash.toString?.()}`)
                console.log(`Hash toBytes(): ${firstEntry.hash.toBytes?.()}`)
            }
        }

        // Analyze num_hashes field
        if ('num_hashes' in firstEntry) {
            console.log(`\nnum_hashes type: ${typeof firstEntry.num_hashes}`)
            console.log(`num_hashes value: ${firstEntry.num_hashes}`)
        }

        // Analyze transactions field
        if ('transactions' in firstEntry) {
            console.log(`\nTransactions type: ${typeof firstEntry.transactions}`)
            console.log(`Transactions is array: ${Array.isArray(firstEntry.transactions)}`)
            console.log(`Transactions length: ${firstEntry.transactions?.length}`)

            if (Array.isArray(firstEntry.transactions) && firstEntry.transactions.length > 0) {
                console.log('\n=== FIRST TRANSACTION DETAILED INSPECTION ===')
                const firstTx = firstEntry.transactions[0]
                console.log(inspectObject(firstTx))

                console.log('\n=== TRANSACTION FIELDS ANALYSIS ===')
                console.log(`Transaction type: ${typeof firstTx}`)
                console.log(`Transaction constructor: ${firstTx?.constructor?.name}`)
                console.log(`Transaction keys: ${Object.keys(firstTx || {})}`)

                // Analyze signatures
                if ('signatures' in firstTx) {
                    console.log(`\nSignatures type: ${typeof firstTx.signatures}`)
                    console.log(`Signatures is array: ${Array.isArray(firstTx.signatures)}`)
                    console.log(`Signatures length: ${firstTx.signatures?.length}`)
                    console.log(`Signatures JSON: ${JSON.stringify(firstTx.signatures, null, 2)}`)

                    if (Array.isArray(firstTx.signatures)) {
                        firstTx.signatures.forEach((sig, index) => {
                            console.log(`\nSignature ${index}:`)
                            console.log(`  Type: ${typeof sig}`)
                            console.log(`  Is array: ${Array.isArray(sig)}`)
                            if (Array.isArray(sig)) {
                                console.log(`  Length: ${sig.length}`)
                                console.log(`  First 10 bytes: [${sig.slice(0, 10).join(', ')}...]`)
                                console.log(`  All data: ${JSON.stringify(sig)}`)
                            } else {
                                console.log(`  Value: ${JSON.stringify(sig)}`)
                            }
                        })
                    }
                }

                // Analyze message
                if ('message' in firstTx) {
                    console.log(`\nMessage type: ${typeof firstTx.message}`)
                    console.log(`Message constructor: ${firstTx.message?.constructor?.name}`)
                    console.log(`Message is array: ${Array.isArray(firstTx.message)}`)
                    console.log(`Message JSON: ${JSON.stringify(firstTx.message, null, 2)}`)

                    if (Array.isArray(firstTx.message)) {
                        console.log(`Message array length: ${firstTx.message.length}`)
                        firstTx.message.forEach((item, index) => {
                            console.log(`\nMessage item ${index}:`)
                            console.log(`  Type: ${typeof item}`)
                            console.log(`  Is array: ${Array.isArray(item)}`)
                            console.log(`  Keys: ${Object.keys(item || {})}`)
                            console.log(`  JSON: ${JSON.stringify(item, null, 2)}`)
                        })
                    } else {
                        console.log(`Message keys: ${Object.keys(firstTx.message || {})}`)
                        console.log(inspectObject(firstTx.message, 0, 3))
                    }
                }
            }
        }
    }
} else {
    console.log('\nNo entries found in result or entries field missing')
}

console.log('\n=== TYPE DEFINITIONS COMPARISON ===')
console.log('Expected according to .d.ts file:')
console.log('- decode_entries should return: ParsedEntry')
console.log('- ParsedEntry should have: { slot: number, entries: Entry[] }')
console.log('- Entry should have: { num_hashes: number, hash: Hash, transactions: VersionedTransaction[] }')
console.log('- Hash should be a class with toString(), toBytes(), equals() methods')
console.log('- Signature should be: number[]')
console.log('- VersionedTransaction should have: { signatures: Signature[], message: VersionedMessage }')

console.log('\n=== ACTUAL DATA STRUCTURE ANALYSIS ===')
console.log('FOUND ISSUES:')
console.log('1. Hash: Expected Hash class, got number[] (32 bytes)')
console.log('2. Signatures: Expected Signature[], got [number[], number[]] (short_vec tuple format)')
console.log('3. Message: Expected VersionedMessage union, got [LegacyMessage|V0Message] (custom serialization)')
console.log('4. Account Keys: Expected Pubkey[], got [number[], ...number[][]] (short_vec tuple format)')
console.log(
    '5. Instructions: Expected CompiledInstruction[], got [number[], ...CompiledInstruction[]] (short_vec tuple format)'
)

console.log('\n=== REQUIRED TSIFY TYPE FIXES ===')
console.log('1. Entry.hash: #[tsify(type = "number[]")]')
console.log('2. VersionedTransaction.signatures: #[tsify(type = "[number[], number[]]")]')
console.log('3. VersionedMessage: #[tsify(type = "[LegacyMessage | V0Message]")]')
console.log('4. LegacyMessage.account_keys: #[tsify(type = "[number[], ...number[][]]")]')
console.log('5. LegacyMessage.instructions: #[tsify(type = "[number[], ...CompiledInstruction[]]")]')
console.log('6. V0Message.account_keys: #[tsify(type = "[number[], ...number[][]]")]')
console.log('7. V0Message.instructions: #[tsify(type = "[number[], ...CompiledInstruction[]]")]')
console.log('8. V0Message.address_table_lookups: #[tsify(type = "[number[], ...MessageAddressTableLookup[]]")]')
console.log('9. CompiledInstruction.accounts: #[tsify(type = "[number[], ...number[]]")]')
console.log('10. CompiledInstruction.data: #[tsify(type = "[number[], ...number[]]")]')
console.log('11. MessageAddressTableLookup.writable_indexes: #[tsify(type = "[number[], ...number[]]")]')
console.log('12. MessageAddressTableLookup.readonly_indexes: #[tsify(type = "[number[], ...number[]]")]')
