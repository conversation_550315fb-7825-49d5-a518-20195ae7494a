import { readFileSync, readdirSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

interface SignatureAnalysis {
    filename: string
    entryIndex: number
    transactionIndex: number
    signaturesLength: number
    signaturesStructure: string
    detailedStructure: any
}

function analyzeSignaturesDetailed(signatures: any): any {
    if (!Array.isArray(signatures)) return { type: 'not_array', value: signatures }
    
    return {
        type: 'array',
        length: signatures.length,
        elements: signatures.map((sig, index) => ({
            index,
            type: Array.isArray(sig) ? 'array' : typeof sig,
            length: Array.isArray(sig) ? sig.length : undefined,
            sample: Array.isArray(sig) ? sig.slice(0, 5) : sig
        }))
    }
}

function findMultiSignatureTransactions(filename: string): SignatureAnalysis[] {
    const results: SignatureAnalysis[] = []
    
    try {
        const data = readFileSync(filename)
        const decoded = decode_entries(1n, data)
        
        if (!decoded?.entries) return results
        
        decoded.entries.forEach((entry, entryIndex) => {
            if (!entry?.transactions) return
            
            entry.transactions.forEach((tx, txIndex) => {
                if (!tx?.signatures) return
                
                const signaturesLength = Array.isArray(tx.signatures) ? tx.signatures.length : 0
                const detailedStructure = analyzeSignaturesDetailed(tx.signatures)
                
                // Look for any interesting patterns, not just multi-signatures
                if (signaturesLength > 0) {
                    results.push({
                        filename,
                        entryIndex,
                        transactionIndex: txIndex,
                        signaturesLength,
                        signaturesStructure: JSON.stringify(detailedStructure, null, 2),
                        detailedStructure
                    })
                }
            })
        })
        
    } catch (error) {
        console.log(`❌ Error processing ${filename}: ${error}`)
    }
    
    return results
}

console.log('=== SEARCHING FOR MULTI-SIGNATURE TRANSACTIONS ===\n')

// Get all shred files
const allFiles = readdirSync('tests/data')
    .filter(f => f.startsWith('shred_') && f.endsWith('.bin'))
    .map(f => `tests/data/${f}`)
    .slice(0, 50) // Test first 50 files

console.log(`Analyzing ${allFiles.length} files for signature patterns...\n`)

const allSignatureAnalyses: SignatureAnalysis[] = []

for (const filename of allFiles) {
    const analyses = findMultiSignatureTransactions(filename)
    allSignatureAnalyses.push(...analyses)
    
    if (analyses.length > 0) {
        console.log(`✅ ${filename}: Found ${analyses.length} transactions`)
    }
}

console.log(`\n=== SIGNATURE PATTERN SUMMARY ===`)
console.log(`Total transactions analyzed: ${allSignatureAnalyses.length}`)

// Group by signature length
const byLength = new Map<number, SignatureAnalysis[]>()
allSignatureAnalyses.forEach(analysis => {
    const length = analysis.signaturesLength
    if (!byLength.has(length)) {
        byLength.set(length, [])
    }
    byLength.get(length)!.push(analysis)
})

console.log('\n=== SIGNATURES LENGTH DISTRIBUTION ===')
Array.from(byLength.entries())
    .sort(([a], [b]) => a - b)
    .forEach(([length, analyses]) => {
        console.log(`Length ${length}: ${analyses.length} transactions`)
    })

// Show detailed examples for different lengths
console.log('\n=== DETAILED SIGNATURE STRUCTURE EXAMPLES ===')

Array.from(byLength.entries())
    .sort(([a], [b]) => a - b)
    .forEach(([length, analyses]) => {
        console.log(`\n--- Signatures Length ${length} ---`)
        const example = analyses[0]
        console.log(`Example from: ${example.filename}`)
        console.log(`Entry ${example.entryIndex}, Transaction ${example.transactionIndex}`)
        console.log('Structure:')
        console.log(example.signaturesStructure)
        
        // Show pattern analysis
        const structure = example.detailedStructure
        if (structure.type === 'array' && structure.elements.length >= 2) {
            const firstElement = structure.elements[0]
            if (firstElement.type === 'array' && firstElement.length === 1) {
                const expectedSigs = firstElement.sample[0]
                const actualSigs = structure.elements.length - 1
                console.log(`Pattern: [length=${expectedSigs}, actual_signatures=${actualSigs}]`)
                
                if (expectedSigs === actualSigs) {
                    console.log('✅ Length matches actual signatures')
                } else {
                    console.log('❌ Length mismatch!')
                }
                
                // Show signature data
                console.log('Signature data elements:')
                structure.elements.slice(1).forEach((sig, index) => {
                    console.log(`  Sig ${index}: ${sig.type}(${sig.length}) - ${JSON.stringify(sig.sample)}...`)
                })
            }
        }
    })

// Look for any transactions with length > 2 (which would indicate multiple signatures)
const multiSigTransactions = allSignatureAnalyses.filter(a => a.signaturesLength > 2)

if (multiSigTransactions.length > 0) {
    console.log(`\n🎯 FOUND ${multiSigTransactions.length} MULTI-SIGNATURE TRANSACTIONS!`)
    multiSigTransactions.forEach(analysis => {
        console.log(`\n📍 ${analysis.filename}`)
        console.log(`   Entry ${analysis.entryIndex}, Transaction ${analysis.transactionIndex}`)
        console.log(`   Signatures length: ${analysis.signaturesLength}`)
        console.log('   Structure:')
        console.log(analysis.signaturesStructure)
    })
} else {
    console.log('\n🔍 No multi-signature transactions found in analyzed files.')
    console.log('All transactions appear to have single signatures with the pattern:')
    console.log('   [length_prefix, signature_data]')
    console.log('   where length_prefix = [1] and signature_data = [64 bytes]')
}

console.log('\n=== CONCLUSION ===')
if (multiSigTransactions.length > 0) {
    console.log('✅ Found multi-signature examples - need to analyze their structure')
} else {
    console.log('✅ All analyzed transactions use single signature pattern')
    console.log('✅ Consistent structure: [number[], number[]] for [length, signature]')
    console.log('✅ For multi-signatures, pattern would likely be: [number[], ...number[][]]')
}
