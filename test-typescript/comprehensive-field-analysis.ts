import { readFileSync, readdirSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

interface FieldAnalysis {
    fieldName: string
    structure: string
    pattern: string
    examples: any[]
    count: number
}

function analyzeShortVecField(field: any, fieldName: string): FieldAnalysis {
    if (!Array.isArray(field)) {
        return {
            fieldName,
            structure: `Not array: ${typeof field}`,
            pattern: 'Invalid',
            examples: [field],
            count: 1
        }
    }
    
    if (field.length === 0) {
        return {
            fieldName,
            structure: 'Empty array',
            pattern: 'Empty',
            examples: [],
            count: 1
        }
    }
    
    const firstElement = field[0]
    if (Array.isArray(firstElement) && firstElement.length === 1) {
        const expectedLength = firstElement[0]
        const actualItems = field.length - 1
        const itemTypes = field.slice(1).map((item, index) => ({
            index,
            type: Array.isArray(item) ? `array(${item.length})` : typeof item,
            sample: Array.isArray(item) ? item.slice(0, 3) : item
        }))
        
        return {
            fieldName,
            structure: `[length=${expectedLength}, actual=${actualItems}]`,
            pattern: expectedLength === actualItems ? 'Valid short_vec' : 'Invalid short_vec',
            examples: itemTypes,
            count: 1
        }
    }
    
    return {
        fieldName,
        structure: `Array(${field.length}) - no length prefix`,
        pattern: 'Non-short_vec array',
        examples: field.slice(0, 3),
        count: 1
    }
}

function analyzeAllFields(filename: string): FieldAnalysis[] {
    const results: FieldAnalysis[] = []
    
    try {
        const data = readFileSync(filename)
        const decoded = decode_entries(1n, data)
        
        if (!decoded?.entries) return results
        
        decoded.entries.forEach((entry, entryIndex) => {
            // Analyze entry.hash
            if (entry.hash) {
                results.push({
                    fieldName: 'Entry.hash',
                    structure: Array.isArray(entry.hash) ? `array(${entry.hash.length})` : typeof entry.hash,
                    pattern: Array.isArray(entry.hash) && entry.hash.length === 32 ? 'Valid hash' : 'Invalid hash',
                    examples: Array.isArray(entry.hash) ? [entry.hash.slice(0, 5)] : [entry.hash],
                    count: 1
                })
            }
            
            if (!entry?.transactions) return
            
            entry.transactions.forEach((tx, txIndex) => {
                // Analyze signatures
                if (tx.signatures) {
                    results.push(analyzeShortVecField(tx.signatures, 'VersionedTransaction.signatures'))
                }
                
                // Analyze message
                if (tx.message) {
                    if (Array.isArray(tx.message)) {
                        const messageType = tx.message.length === 1 ? 'Legacy' : 
                                          tx.message.length === 2 ? 'V0' : 'Unknown'
                        results.push({
                            fieldName: 'VersionedTransaction.message',
                            structure: `Array(${tx.message.length}) - ${messageType}`,
                            pattern: messageType,
                            examples: [tx.message.map((item, i) => ({
                                index: i,
                                type: typeof item,
                                keys: typeof item === 'object' && item ? Object.keys(item) : []
                            }))],
                            count: 1
                        })
                        
                        // Analyze message content
                        const messageObj = tx.message[tx.message.length - 1] // Last element is the actual message
                        if (messageObj && typeof messageObj === 'object') {
                            // Account keys
                            if (messageObj.account_keys) {
                                results.push(analyzeShortVecField(messageObj.account_keys, 'Message.account_keys'))
                            }
                            
                            // Instructions
                            if (messageObj.instructions) {
                                results.push(analyzeShortVecField(messageObj.instructions, 'Message.instructions'))
                                
                                // Analyze instruction fields
                                if (Array.isArray(messageObj.instructions) && messageObj.instructions.length > 1) {
                                    const firstInstruction = messageObj.instructions[1] // Skip length prefix
                                    if (firstInstruction && typeof firstInstruction === 'object') {
                                        if (firstInstruction.accounts) {
                                            results.push(analyzeShortVecField(firstInstruction.accounts, 'CompiledInstruction.accounts'))
                                        }
                                        if (firstInstruction.data) {
                                            results.push(analyzeShortVecField(firstInstruction.data, 'CompiledInstruction.data'))
                                        }
                                    }
                                }
                            }
                            
                            // Address table lookups (V0 only)
                            if (messageObj.address_table_lookups) {
                                results.push(analyzeShortVecField(messageObj.address_table_lookups, 'V0Message.address_table_lookups'))
                                
                                // Analyze lookup fields
                                if (Array.isArray(messageObj.address_table_lookups) && messageObj.address_table_lookups.length > 1) {
                                    const firstLookup = messageObj.address_table_lookups[1] // Skip length prefix
                                    if (firstLookup && typeof firstLookup === 'object') {
                                        if (firstLookup.writable_indexes) {
                                            results.push(analyzeShortVecField(firstLookup.writable_indexes, 'MessageAddressTableLookup.writable_indexes'))
                                        }
                                        if (firstLookup.readonly_indexes) {
                                            results.push(analyzeShortVecField(firstLookup.readonly_indexes, 'MessageAddressTableLookup.readonly_indexes'))
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            })
        })
        
    } catch (error) {
        console.log(`❌ Error processing ${filename}: ${error}`)
    }
    
    return results
}

console.log('=== COMPREHENSIVE FIELD ANALYSIS ===\n')

// Get more files for comprehensive analysis
const allFiles = readdirSync('tests/data')
    .filter(f => f.startsWith('shred_') && f.endsWith('.bin'))
    .map(f => `tests/data/${f}`)
    .slice(0, 100) // Analyze first 100 files

console.log(`Analyzing ${allFiles.length} files for all field patterns...\n`)

const allAnalyses: FieldAnalysis[] = []

for (const filename of allFiles) {
    try {
        const analyses = analyzeAllFields(filename)
        allAnalyses.push(...analyses)
        
        if (analyses.length > 0) {
            console.log(`✅ ${filename}: Found ${analyses.length} field instances`)
        }
    } catch (error) {
        console.log(`❌ ${filename}: Failed to analyze`)
    }
}

console.log(`\n=== FIELD ANALYSIS SUMMARY ===`)
console.log(`Total field instances analyzed: ${allAnalyses.length}`)

// Group by field name
const byField = new Map<string, FieldAnalysis[]>()
allAnalyses.forEach(analysis => {
    const fieldName = analysis.fieldName
    if (!byField.has(fieldName)) {
        byField.set(fieldName, [])
    }
    byField.get(fieldName)!.push(analysis)
})

console.log('\n=== FIELD PATTERN ANALYSIS ===')

Array.from(byField.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .forEach(([fieldName, analyses]) => {
        console.log(`\n--- ${fieldName} ---`)
        console.log(`Total instances: ${analyses.length}`)
        
        // Group by pattern
        const patternCounts = new Map<string, number>()
        const patternExamples = new Map<string, any>()
        
        analyses.forEach(analysis => {
            const pattern = analysis.pattern
            patternCounts.set(pattern, (patternCounts.get(pattern) || 0) + 1)
            if (!patternExamples.has(pattern)) {
                patternExamples.set(pattern, analysis)
            }
        })
        
        Array.from(patternCounts.entries())
            .sort(([,a], [,b]) => b - a)
            .forEach(([pattern, count]) => {
                const percentage = ((count / analyses.length) * 100).toFixed(1)
                console.log(`  ${pattern}: ${count} instances (${percentage}%)`)
                
                const example = patternExamples.get(pattern)!
                console.log(`    Structure: ${example.structure}`)
                if (example.examples.length > 0) {
                    console.log(`    Example: ${JSON.stringify(example.examples[0])}`)
                }
            })
    })

console.log('\n=== TYPESCRIPT TYPE RECOMMENDATIONS ===')
console.log('Based on comprehensive field analysis:')
console.log('')

// Generate recommendations for each field
Array.from(byField.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .forEach(([fieldName, analyses]) => {
        const validShortVec = analyses.filter(a => a.pattern === 'Valid short_vec').length
        const totalInstances = analyses.length
        const percentage = ((validShortVec / totalInstances) * 100).toFixed(1)
        
        if (validShortVec > 0) {
            console.log(`${fieldName}:`)
            console.log(`  Pattern: short_vec (${validShortVec}/${totalInstances} = ${percentage}%)`)
            
            // Determine the element type
            if (fieldName.includes('signatures')) {
                console.log(`  TypeScript: [number[], ...Signature[]]`)
                console.log(`  Rust: #[tsify(type = "[number[], ...Signature[]]")]`)
            } else if (fieldName.includes('account_keys')) {
                console.log(`  TypeScript: [number[], ...number[][]]`)
                console.log(`  Rust: #[tsify(type = "[number[], ...number[][]]")]`)
            } else if (fieldName.includes('instructions')) {
                console.log(`  TypeScript: [number[], ...CompiledInstruction[]]`)
                console.log(`  Rust: #[tsify(type = "[number[], ...CompiledInstruction[]]")]`)
            } else if (fieldName.includes('address_table_lookups')) {
                console.log(`  TypeScript: [number[], ...MessageAddressTableLookup[]]`)
                console.log(`  Rust: #[tsify(type = "[number[], ...MessageAddressTableLookup[]]")]`)
            } else if (fieldName.includes('accounts') || fieldName.includes('data') || fieldName.includes('indexes')) {
                console.log(`  TypeScript: [number[], ...number[]]`)
                console.log(`  Rust: #[tsify(type = "[number[], ...number[]]")]`)
            }
            console.log('')
        } else if (fieldName.includes('hash')) {
            console.log(`${fieldName}:`)
            console.log(`  Pattern: 32-byte array`)
            console.log(`  TypeScript: number[]`)
            console.log(`  Rust: #[tsify(type = "number[]")]`)
            console.log('')
        } else if (fieldName.includes('message')) {
            console.log(`${fieldName}:`)
            console.log(`  Pattern: Legacy or V0 message wrapper`)
            console.log(`  TypeScript: [LegacyMessage] | [number, V0Message]`)
            console.log(`  Rust: #[tsify(type = "[LegacyMessage] | [number, V0Message]")]`)
            console.log('')
        }
    })
